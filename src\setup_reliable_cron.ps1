# PowerShell script to create a reliable scheduled task
$TaskName = "GitHubTimelineUpdates"
$ScriptPath = "C:\Users\<USER>\Documents\github-timeline-ParnaRoyChowdhury777\src\run_cron.bat"

# Delete existing task if it exists
try {
    Unregister-ScheduledTask -TaskName $TaskName -Confirm:$false -ErrorAction SilentlyContinue
    Write-Host "Deleted existing task (if any)"
} catch {
    Write-Host "No existing task to delete"
}

# Create the action
$Action = New-ScheduledTaskAction -Execute $ScriptPath

# Create the trigger (every 5 minutes)
$Trigger = New-ScheduledTaskTrigger -RepetitionInterval (New-TimeSpan -Minutes 5) -RepetitionDuration (New-TimeSpan -Days 365) -Once -At (Get-Date)

# Create settings with power management disabled
$Settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable -DontStopOnIdleEnd

# Register the task
Register-ScheduledTask -TaskName $TaskName -Action $Action -Trigger $Trigger -Settings $Settings -Description "GitHub Timeline Email Updates - Every 5 minutes"

Write-Host "Task created successfully!"
Write-Host "Task Name: $TaskName"
Write-Host "Script: $ScriptPath"
Write-Host "Schedule: Every 5 minutes"

# Show task status
Get-ScheduledTask -TaskName $TaskName | Format-Table TaskName, State, @{Name="NextRunTime";Expression={(Get-ScheduledTask $_.TaskName | Get-ScheduledTaskInfo).NextRunTime}}
