<?php
require_once 'functions.php';
session_start();

$message = '';
$step = 1;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['email'])) {
        $email = $_POST['email'];
        $_SESSION['email'] = $email;
        $code = generateVerificationCode();
        $_SESSION['verification_code'] = $code;
        if (sendVerificationEmail($email, $code)) {
            $message = "<p class='success'>Verification code sent to $email</p>";
        } else {
            $message = "<p class='error'>Failed to send verification code. Please try again later.</p>";
        }
        $step = 2;
    }

    if (isset($_POST['verification_code'])) {
        if ($_POST['verification_code'] === $_SESSION['verification_code']) {
            registerEmail($_SESSION['email']);
            $message = "<p class='success'><PERSON>ail verified and registered successfully!</p>";
            $step = 3; // Optional final screen
        } else {
            $message = "<p class='error'>Invalid verification code.</p>";
            $step = 2;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>Email Verification</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <style>
        :root {
            --primary: #0d6efd;
            --text: #1f2937;
            --radius: 12px;
            --transition: 0.3s ease;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f4f6f8;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .form-card {
            background-color: #ffffff;
            padding: 40px 35px;
            border-radius: var(--radius);
            box-shadow: 0 8px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 420px;
            transition: var(--transition);
            position: relative;
        }

        h2 {
            margin-bottom: 20px;
            font-size: 1.6rem;
            color: var(--primary);
            text-align: center;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text);
            font-size: 15px;
        }

        .inputForm,
        input[type="text"],
        input[type="email"] {
            width: 100%;
            padding: 12px 14px;
            margin-bottom: 20px;
            border: 1.5px solid #ecedec;
            border-radius: 10px;
            font-size: 15px;
            box-sizing: border-box;
            transition: border var(--transition);
            background-color: rgb(249, 249, 249);
        }

        input:focus {
            border-color: var(--primary);
            outline: none;
        }

        button {
            width: 100%;
            padding: 12px;
            background-color: var(--primary);
            border: none;
            border-radius: 10px;
            color: white;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color var(--transition);
        }

        button:hover {
            background-color: #0b5ed7;
        }

        .step {
            display: none;
            animation: fadeIn 0.5s ease-in-out forwards;
        }

        .step.active {
            display: block;
        }

        .form-footer {
            margin-top: 10px;
            text-align: center;
        }

        .success {
            background-color: #d1e7dd;
            padding: 10px;
            border-radius: 8px;
            color: #0f5132;
            font-size: 14px;
            margin-bottom: 15px;
        }

        .error {
            background-color: #f8d7da;
            padding: 10px;
            border-radius: 8px;
            color: #842029;
            font-size: 14px;
            margin-bottom: 15px;
        }

        @keyframes fadeIn {
            0% {
                opacity: 0;
                transform: translateY(10px);
            }

            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @media (max-width: 500px) {
            .form-card {
                padding: 30px 20px;
            }

            h2 {
                font-size: 1.4rem;
            }

            button {
                font-size: 15px;
            }
        }
    </style>
</head>

<body>

    <div class="form-card">
        <h2>Email Verification</h2>
        <?= $message ?>

        <!-- Step 1: Email Input -->
        <form method="POST" id="step-1" class="step <?= $step === 1 ? 'active' : '' ?>">
            <label for="email">Email Address</label>
            <input type="email" name="email" placeholder="Enter your email" required />
            <button id="submit-email">Send Verification Code</button>
        </form>

        <!-- Step 2: Verification Code Input -->
        <form method="POST" id="step-2" class="step <?= $step === 2 ? 'active' : '' ?>">
            <label for="verification_code">Verification Code</label>
            <input type="text" name="verification_code" placeholder="Enter 6-digit code" maxlength="6" required />
            <button id="submit-verification">Verify</button>
        </form>

        <!-- Step 3: Final Success Screen -->
        <div class="step <?= $step === 3 ? 'active' : '' ?>">
            <p class="success">🎉 Your email has been successfully verified and registered!</p>
        </div>
    </div>

    <script>
        function backToStep1() {
            document.getElementById("step-1").classList.add("active");
            document.getElementById("step-2").classList.remove("active");
        }
    </script>

</body>

</html>