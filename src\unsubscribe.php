<?php
require_once 'functions.php';

// TODO: Implement the form and logic for email unsubscription.

session_start();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['unsubscribe_email'])) {
        $email = $_POST['unsubscribe_email'];
        $_SESSION['unsubscribe_email'] = $email;
        $code = generateVerificationCode();
        $_SESSION['unsubscribe_code'] = $code;
        unsubscribeEmail($email, $code);
        echo "<p>Unsubscribe verification code sent to $email</p>";
    }

    if (isset($_POST['unsubscribe_verification_code'])) {
        if ($_POST['unsubscribe_verification_code'] === $_SESSION['unsubscribe_code']) {
            unsubscribeEmail($_SESSION['unsubscribe_email']);
            echo "<p>Email unsubscribed successfully!</p>";
        } else {
            echo "<p>Invalid unsubscribe verification code.</p>";
        }
    }
}
?>

<form method="POST">
    <input type="email" name="unsubscribe_email" required>
    <button id="submit-unsubscribe">Unsubscribe</button>
</form>

<form method="POST">
    <input type="text" name="unsubscribe_verification_code">
    <button id="verify-unsubscribe">Verify</button>
</form>