<?php
require_once 'functions.php';
session_start();

$message = '';
$step = 1;

// Get email from URL parameter if provided
$prefilledEmail = isset($_GET['email']) ? htmlspecialchars($_GET['email']) : '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['unsubscribe_email'])) {
        $email = $_POST['unsubscribe_email'];
        $_SESSION['unsubscribe_email'] = $email;
        $code = generateVerificationCode();
        $_SESSION['unsubscribe_code'] = $code;
        if (sendUnsubscribeEmail($email, $code)) {
            $message = "<div class='message success'><i class='icon'>✓</i>Verification code sent to $email</div>";
            $step = 2;
        } else {
            $message = "<div class='message error'><i class='icon'>✕</i>Failed to send verification code. Please try again.</div>";
        }
    }

    if (isset($_POST['unsubscribe_verification_code'])) {
        if ($_POST['unsubscribe_verification_code'] === $_SESSION['unsubscribe_code']) {
            if (unsubscribeEmail($_SESSION['unsubscribe_email'])) {
                $message = "<div class='message success'><i class='icon'>🎉</i>Successfully unsubscribed from GitHub updates!</div>";
                $step = 3;
            } else {
                $message = "<div class='message error'><i class='icon'>⚠</i>Failed to unsubscribe. Email may not be registered.</div>";
            }
        } else {
            $message = "<div class='message error'><i class='icon'>✕</i>Invalid verification code. Please try again.</div>";
            $step = 2;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unsubscribe from GitHub Updates</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            padding: 20px;
            margin: 0;
        }

        .container {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            padding: 40px;
            width: 100%;
            max-width: 480px;
            margin: 30px auto;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
            animation: slideIn 0.4s ease-out;
        }

        @keyframes slideIn {
            0% {
                opacity: 0;
                transform: translateY(20px);
            }

            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        h1 {
            color: #1e293b;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            text-align: center;
        }

        .subtitle {
            color: #64748b;
            font-size: 15px;
            text-align: center;
            line-height: 1.5;
        }

        .message {
            border-radius: 16px;
            padding: 20px 24px;
            margin-bottom: 32px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 12px;
            animation: messageSlide 0.4s cubic-bezier(0.16, 1, 0.3, 1);
            border: 1px solid;
        }

        @keyframes messageSlide {
            0% {
                opacity: 0;
                transform: translateX(-20px);
            }

            100% {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .message.success {
            background: #f0f9ff;
            color: #0c4a6e;
            border-color: #bae6fd;
        }

        .message.error {
            background: #fef2f2;
            color: #991b1b;
            border-color: #fecaca;
        }

        .message .icon {
            font-size: 18px;
            font-weight: bold;
        }

        .form-section {
            margin-bottom: 32px;
        }

        .form-group {
            position: relative;
            margin-bottom: 24px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
            font-size: 14px;
            letter-spacing: 0.5px;
            text-transform: uppercase;
        }

        input[type="email"],
        input[type="text"] {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 500;
            background: #fafafa;
            transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
            outline: none;
        }

        input[type="email"]:focus,
        input[type="text"]:focus {
            border-color: #3b82f6;
            background: white;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        input[readonly] {
            background: #f3f4f6;
            cursor: not-allowed;
        }

        .btn {
            width: 100%;
            padding: 18px 24px;
            border: none;
            border-radius: 15px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
            position: relative;
            overflow: hidden;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn:active {
            transform: translateY(-1px);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 32px;
            gap: 12px;
        }

        .step {
            width: 40px;
            height: 4px;
            background: #e5e7eb;
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        .step.active {
            background: #3b82f6;
        }

        .back-link {
            text-align: center;
            margin-top: 32px;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .back-link.step-3-visible {
            opacity: 1;
            visibility: visible;
        }

        .back-link a {
            color: #3b82f6;
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .back-link a:hover {
            background: #eff6ff;
            transform: translateX(-2px);
        }



        @media (max-width: 640px) {
            .container {
                padding: 24px;
                margin: 10px;
                border-radius: 12px;
            }

            h1 {
                font-size: 22px;
            }

        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>Unsubscribe</h1>
            <p class="subtitle">We're sorry to see you go. Complete the steps below to unsubscribe from GitHub updates.</p>
        </div>

        <div class="step-indicator">
            <div class="step <?= $step >= 1 ? 'active' : '' ?>"></div>
            <div class="step <?= $step >= 2 ? 'active' : '' ?>"></div>
            <div class="step <?= $step >= 3 ? 'active' : '' ?>"></div>
        </div>

        <?= $message ?>

        <!-- Email Input Form -->
        <div class="form-section">
            <form method="POST">
                <div class="form-group">
                    <label for="unsubscribe_email">Email Address</label>
                    <input type="email" name="unsubscribe_email" value="<?= $prefilledEmail ?>" required readonly>
                </div>
                <button type="submit" class="btn btn-primary">Send Verification Code</button>
            </form>
        </div>

        <!-- Verification Code Form -->
        <div class="form-section">
            <form method="POST">
                <div class="form-group">
                    <label for="unsubscribe_verification_code">Verification Code</label>
                    <input type="text" name="unsubscribe_verification_code" placeholder="Enter 6-digit code" maxlength="6" pattern="[0-9]{6}">
                </div>
                <button type="submit" class="btn btn-danger">Confirm Unsubscribe</button>
            </form>
        </div>

        <div class="back-link <?= $step === 3 ? 'step-3-visible' : '' ?>">
            <a href="index.php">← Return to Registration</a>
        </div>
    </div>

    <script>
        // Add smooth interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-focus on verification code input when step 2
            <?php if ($step === 2): ?>
                setTimeout(() => {
                    const codeInput = document.querySelector('input[name="unsubscribe_verification_code"]');
                    if (codeInput) codeInput.focus();
                }, 500);
            <?php endif; ?>

            // Format verification code input
            const codeInput = document.querySelector('input[name="unsubscribe_verification_code"]');
            if (codeInput) {
                codeInput.addEventListener('input', function(e) {
                    e.target.value = e.target.value.replace(/\D/g, '');
                });
            }

            // Add loading state to buttons
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    const form = this.closest('form');
                    if (form.checkValidity()) {
                        this.style.opacity = '0.7';
                        this.innerHTML = '<span style="opacity: 0.7;">Processing...</span>';
                    }
                });
            });
        });
    </script>
</body>

</html>