#!/bin/bash

# This script should set up a CRON job to run cron.php every 5 minutes.
# You need to implement the CRON setup logic here.

# Get the current directory (where the script is located)
SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
CRON_PHP_PATH="$SCRIPT_DIR/cron.php"

# Check if cron.php exists
if [ ! -f "$CRON_PHP_PATH" ]; then
    echo "Error: cron.php not found at $CRON_PHP_PATH"
    exit 1
fi

# Create the cron job entry (runs every 5 minutes)
CRON_JOB="*/5 * * * * /usr/bin/php $CRON_PHP_PATH >> $SCRIPT_DIR/cron_output.log 2>&1"

# Check if the cron job already exists
if crontab -l 2>/dev/null | grep -F "$CRON_PHP_PATH" > /dev/null; then
    echo "CRON job already exists for GitHub timeline updates"
    echo "Current CRON jobs:"
    crontab -l | grep -F "$CRON_PHP_PATH"
else
    # Add the cron job
    echo "Adding CRON job for GitHub timeline updates..."
    
    # Get current crontab, add new job, and install it
    (crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -
    
    if [ $? -eq 0 ]; then
        echo "CRON job added successfully!"
        echo "The job will run every 5 minutes and execute: $CRON_PHP_PATH"
        echo "Logs will be written to: $SCRIPT_DIR/cron_output.log"
        echo ""
        echo "CRON job details:"
        echo "$CRON_JOB"
    else
        echo "Error: Failed to add CRON job"
        exit 1
    fi
fi

# Create log files with proper permissions
touch "$SCRIPT_DIR/cron_output.log"
touch "$SCRIPT_DIR/cron_log.txt"
chmod 644 "$SCRIPT_DIR/cron_output.log"
chmod 644 "$SCRIPT_DIR/cron_log.txt"

# Display current crontab
echo ""
echo "Current CRON jobs:"
crontab -l

