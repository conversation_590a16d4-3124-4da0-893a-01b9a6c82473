#!/bin/bash

# This script sets up a CRON job to run cron.php every 5 minutes.
# For Windows systems, this script detects the OS and uses PowerShell instead.

# Detect operating system
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" || "$OSTYPE" == "win32" ]]; then
    echo "Windows detected. Using PowerShell to create scheduled task..."

    # Get the current directory
    SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)

    # Convert to Windows path format
    WIN_SCRIPT_DIR=$(cygpath -w "$SCRIPT_DIR" 2>/dev/null || echo "$SCRIPT_DIR")

    # Create PowerShell script to set up scheduled task
    powershell.exe -Command "
    \$scriptDir = '$WIN_SCRIPT_DIR'
    \$batchFile = Join-Path \$scriptDir 'run_cron.bat'

    # Create batch file if it doesn't exist
    if (-not (Test-Path \$batchFile)) {
        \$batchContent = @'
@echo off
cd /d `"$WIN_SCRIPT_DIR`"
echo [%date% %time%] Starting cron job >> cron_output.log
`"C:\php-8.4.8\php.exe`" cron.php >> cron_output.log 2>&1
echo [%date% %time%] Cron job completed >> cron_output.log
'@
        Set-Content -Path \$batchFile -Value \$batchContent
        Write-Host 'Created batch file: ' \$batchFile
    }

    # Create scheduled task
    \$taskName = 'GitHubTimelineUpdates'
    \$action = New-ScheduledTaskAction -Execute \$batchFile
    \$trigger = New-ScheduledTaskTrigger -Once -At (Get-Date) -RepetitionInterval (New-TimeSpan -Minutes 5) -RepetitionDuration (New-TimeSpan -Days 365)
    \$settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable -DontStopOnIdleEnd

    try {
        Unregister-ScheduledTask -TaskName \$taskName -Confirm:\$false -ErrorAction SilentlyContinue
        Register-ScheduledTask -TaskName \$taskName -Action \$action -Trigger \$trigger -Settings \$settings
        Write-Host 'Scheduled task created successfully!'
        Write-Host 'Task will run every 5 minutes'
    } catch {
        Write-Host 'Error creating scheduled task: ' \$_.Exception.Message
    }
    "

    exit 0
fi

# Linux/Unix CRON setup
echo "Unix/Linux detected. Setting up traditional CRON job..."

# Get the current directory (where the script is located)
SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
CRON_PHP_PATH="$SCRIPT_DIR/cron.php"

# Check if cron.php exists
if [ ! -f "$CRON_PHP_PATH" ]; then
    echo "Error: cron.php not found at $CRON_PHP_PATH"
    exit 1
fi

# Create the cron job entry (runs every 5 minutes)
CRON_JOB="*/5 * * * * /usr/bin/php $CRON_PHP_PATH >> $SCRIPT_DIR/cron_output.log 2>&1"

# Check if the cron job already exists
if crontab -l 2>/dev/null | grep -F "$CRON_PHP_PATH" > /dev/null; then
    echo "CRON job already exists for GitHub timeline updates"
    echo "Current CRON jobs:"
    crontab -l | grep -F "$CRON_PHP_PATH"
else
    # Add the cron job
    echo "Adding CRON job for GitHub timeline updates..."

    # Get current crontab, add new job, and install it
    (crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -

    if [ $? -eq 0 ]; then
        echo "CRON job added successfully!"
        echo "The job will run every 5 minutes and execute: $CRON_PHP_PATH"
        echo "Logs will be written to: $SCRIPT_DIR/cron_output.log"
        echo ""
        echo "CRON job details:"
        echo "$CRON_JOB"
    else
        echo "Error: Failed to add CRON job"
        exit 1
    fi
fi

# Create log files with proper permissions
touch "$SCRIPT_DIR/cron_output.log"
touch "$SCRIPT_DIR/cron_log.txt"
chmod 644 "$SCRIPT_DIR/cron_output.log"
chmod 644 "$SCRIPT_DIR/cron_log.txt"

# Display current crontab
echo ""
echo "Current CRON jobs:"
crontab -l

