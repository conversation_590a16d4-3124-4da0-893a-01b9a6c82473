<?php
/**
 * PHP Configuration Checker for GitHub Timeline Cron Job
 * This script checks if required PHP extensions are available
 */

echo "=== PHP Configuration Check ===\n";
echo "PHP Version: " . PHP_VERSION . "\n";
echo "PHP SAPI: " . php_sapi_name() . "\n\n";

$required_extensions = ['curl', 'openssl', 'json'];
$missing_extensions = [];
$available_extensions = [];

foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        $available_extensions[] = $ext;
        echo "✓ $ext extension: AVAILABLE\n";
    } else {
        $missing_extensions[] = $ext;
        echo "✗ $ext extension: MISSING\n";
    }
}

echo "\n=== Configuration Status ===\n";
if (empty($missing_extensions)) {
    echo "✓ All required extensions are available!\n";
    echo "✓ Cron job should work properly.\n";
} else {
    echo "✗ Missing extensions: " . implode(', ', $missing_extensions) . "\n";
    echo "✗ Cron job will fail until these are enabled.\n\n";
    
    echo "=== How to Fix ===\n";
    echo "1. Find your php.ini file location:\n";
    echo "   php --ini\n\n";
    echo "2. Edit php.ini and uncomment these lines:\n";
    foreach ($missing_extensions as $ext) {
        echo "   extension=$ext\n";
    }
    echo "\n3. Restart your web server/PHP service\n";
    echo "4. Run this script again to verify\n";
}

echo "\n=== Testing Basic Functionality ===\n";

// Test file operations
if (is_writable(__DIR__)) {
    echo "✓ Directory is writable\n";
} else {
    echo "✗ Directory is not writable\n";
}

// Test email configuration
echo "SMTP Configuration:\n";
echo "  SMTP: " . ini_get('SMTP') . "\n";
echo "  smtp_port: " . ini_get('smtp_port') . "\n";
echo "  sendmail_from: " . ini_get('sendmail_from') . "\n";

// Test if we can create log files
$test_log = __DIR__ . '/test_log.txt';
if (file_put_contents($test_log, "Test log entry\n")) {
    echo "✓ Can write log files\n";
    unlink($test_log); // Clean up
} else {
    echo "✗ Cannot write log files\n";
}

echo "\n=== Manual Test ===\n";
echo "To manually test the cron job:\n";
echo "php " . __DIR__ . "/cron.php\n";
