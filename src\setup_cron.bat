@echo off
REM setup_cron.bat - Windows batch script to setup scheduled task for GitHub timeline updates

REM Get the current directory
set SCRIPT_DIR=%~dp0
set CRON_PHP_PATH=%SCRIPT_DIR%cron.php

REM Check if cron.php exists
if not exist "%CRON_PHP_PATH%" (
    echo Error: cron.php not found at %CRON_PHP_PATH%
    pause
    exit /b 1
)

REM Find PHP executable
where php >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: PHP not found in PATH. Please install PHP or add it to your PATH.
    echo Common PHP locations:
    echo - C:\php\php.exe
    echo - C:\xampp\php\php.exe
    echo - C:\wamp\bin\php\php8.x.x\php.exe
    pause
    exit /b 1
)

echo Setting up Windows Scheduled Task for GitHub timeline updates...
echo.

REM Create the scheduled task (runs every 5 minutes)
schtasks /create /tn "GitHubTimelineUpdates" /tr "php \"%CRON_PHP_PATH%\"" /sc minute /mo 5 /f

if %errorlevel% equ 0 (
    echo.
    echo Scheduled task created successfully!
    echo Task Name: GitHubTimelineUpdates
    echo Schedule: Every 5 minutes
    echo Command: php "%CRON_PHP_PATH%"
    echo.
    echo Logs will be written to:
    echo - %SCRIPT_DIR%cron_log.txt
    echo - %SCRIPT_DIR%cron_output.log
) else (
    echo.
    echo Error: Failed to create scheduled task
    echo Please run this script as Administrator
    pause
    exit /b 1
)

REM Create log files
type nul > "%SCRIPT_DIR%cron_output.log"
type nul > "%SCRIPT_DIR%cron_log.txt"

echo.
echo Current scheduled tasks related to GitHub:
schtasks /query /tn "GitHubTimelineUpdates" /fo table

echo.
echo Setup complete! The task will run every 5 minutes.
echo To manually run the task: schtasks /run /tn "GitHubTimelineUpdates"
echo To delete the task: schtasks /delete /tn "GitHubTimelineUpdates" /f
echo.
pause