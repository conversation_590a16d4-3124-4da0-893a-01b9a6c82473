<?php

/**
 * Generate a 6-digit numeric verification code.
 */
function generateVerificationCode(): string
{
  // TODO: Implement this function
  return str_pad(rand(0, 999999), 6, '0', STR_PAD_LEFT);
}

/**
 * Send a verification code to an email.
 */
function sendVerificationEmail(string $email, string $code): bool
{
  // TODO: Implement this function

  ini_set("SMTP", "localhost");
  ini_set("smtp_port", "1025");
  ini_set("sendmail_from", "<EMAIL>");

  $subject = "Your Verification Code";
  $message = "<p>Your verification code is: <strong>$code</strong></p>";
  $headers = "From: <EMAIL>\r\n";
  $headers .= "Content-type: text/html\r\n";

  return mail($email, $subject, $message, $headers);
}

/**
 * Register an email by storing it in a file.
 */
function registerEmail(string $email): bool
{
  $file = __DIR__ . '/registered_emails.txt';
  // TODO: Implement this function

  if (!file_exists($file)) {
    file_put_contents($file, '');
  }

  $emails = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);

  if (!in_array($email, $emails)) {
    file_put_contents($file, $email . PHP_EOL, FILE_APPEND);
    return true;
  }

  return false;
}

/**
 * Unsubscribe an email by removing it from the list.
 */
function unsubscribeEmail(string $email): bool
{
  $file = __DIR__ . '/registered_emails.txt';
  // TODO: Implement this function
  if (!file_exists($file)) {
    return false;
  }

  $emails = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
  $filtered = array_filter($emails, fn($e) => trim($e) !== $email);

  if (count($emails) === count($filtered)) {
    return false; // Email not found
  }

  file_put_contents($file, implode(PHP_EOL, $filtered) . PHP_EOL);
  return true;
}

/**
 * Fetch GitHub timeline.
 */
function fetchGitHubTimeline()
{
  // TODO: Implement this function
  $url = 'https://api.github.com/events';

  $ch = curl_init($url);
  curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
  curl_setopt($ch, CURLOPT_USERAGENT, 'GitHubTimelineApp/1.0');
  curl_setopt($ch, CURLOPT_TIMEOUT, 30);
  curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
  curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);

  $result = curl_exec($ch);
  $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
  $error = curl_error($ch);
  curl_close($ch);

  if ($error) {
    error_log("cURL Error: " . $error);
    return false;
  }

  if ($httpCode !== 200) {
    error_log("GitHub API returned HTTP " . $httpCode);
    return false;
  }

  return $result;
}

/**
 * Format GitHub timeline data. Returns a valid HTML string.
 */
function formatGitHubData(array $data): string
{
  // TODO: Implement this function
  if (empty($data)) {
    return "<p>No GitHub events found.</p>";
  }

  $html = "<h2>Latest GitHub Activity</h2>";
  $html .= "<table border='1' style='border-collapse: collapse; width: 100%;'>";
  $html .= "<tr style='background-color: #f2f2f2;'><th>Event Type</th><th>User</th><th>Repository</th><th>Time</th></tr>";

  $limitedData = array_slice($data, 0, 10);

  foreach ($limitedData as $item) {
    $event = htmlspecialchars($item['type'] ?? 'Unknown');
    $user = htmlspecialchars($item['actor']['login'] ?? 'Unknown');
    $repo = htmlspecialchars($item['repo']['name'] ?? 'Unknown');
    $time = isset($item['created_at']) ? date('M j, H:i', strtotime($item['created_at'])) : 'Unknown';

    $html .= "<tr>";
    $html .= "<td>$event</td>";
    $html .= "<td>$user</td>";
    $html .= "<td>$repo</td>";
    $html .= "<td>$time</td>";
    $html .= "</tr>";
  }

  $html .= "</table>";
  return $html;
}

/**
 * Send the formatted GitHub updates to registered emails.
 */
function sendGitHubUpdatesToSubscribers(): void
{
  $file = __DIR__ . '/registered_emails.txt';
  // TODO: Implement this function

  if (!file_exists($file)) {
    error_log("No registered emails file found");
    return;
  }

  $emails = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
  if (empty($emails)) {
    error_log("No emails found in registered emails file");
    return;
  }

  // Fetch GitHub data
  $data = fetchGitHubTimeline();
  if ($data === false) {
    error_log("Failed to fetch GitHub timeline");
    return;
  }

  $decodedData = json_decode($data, true);
  if (!is_array($decodedData)) {
    error_log("Failed to decode GitHub timeline JSON");
    return;
  }

  // Check for new updates (inline the hasNewUpdates logic)
  $lastRunFile = __DIR__ . '/last_run_data.json';
  $isNew = true;

  if (file_exists($lastRunFile)) {
    $lastData = json_decode(file_get_contents($lastRunFile), true);
    if (!empty($decodedData) && !empty($lastData)) {
      $isNew = ($decodedData[0]['id'] ?? '') !== ($lastData[0]['id'] ?? '');
    }
  }

  if (!$isNew) {
    error_log("No new updates found, skipping email send");
    return;
  }

  // Save new data for next time
  file_put_contents($lastRunFile, json_encode($decodedData));

  // Format HTML and send email
  $html = formatGitHubData($decodedData);
  $emailsSent = 0;

  foreach ($emails as $email) {
    $email = trim($email);
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
      error_log("Invalid email address: $email");
      continue;
    }

    $unsubscribeLink = "http://localhost/unsubscribe.php?email=" . urlencode($email);
    $body = $html . "<hr><p><small><a href=\"$unsubscribeLink\">Unsubscribe from these updates</a></small></p>";

    $headers = "From: <EMAIL>\r\n";
    $headers .= "Content-type: text/html; charset=UTF-8\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";

    if (mail($email, "New GitHub Activity", $body, $headers)) {
      $emailsSent++;
    } else {
      error_log("Failed to send email to: $email");
    }
  }

  error_log("Successfully sent emails to $emailsSent recipients");
}
