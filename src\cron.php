<?php
require_once 'functions.php';
// This script should send GitHub updates to registered emails every 5 minutes.
// You need to implement this functionality.

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Log the execution
$logFile = __DIR__ . '/cron_log.txt';
$timestamp = date('Y-m-d H:i:s');

function logMessage($message)
{
    global $logFile, $timestamp;
    $logEntry = "[{$timestamp}] {$message}\n";
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

try {
    logMessage("CRON job started");

    // Send GitHub updates to all subscribers
    sendGitHubUpdatesToSubscribers();

    logMessage("GitHub timeline updates sent successfully");
} catch (Exception $e) {
    logMessage("Error occurred: " . $e->getMessage());
    error_log("CRON job error: " . $e->getMessage());
} catch (Throwable $e) {
    logMessage("Fatal error occurred: " . $e->getMessage());
    error_log("CRON job fatal error: " . $e->getMessage());
}

logMessage("CRON job completed");
