<?php
require_once 'functions.php';
// This script should send GitHub updates to registered emails every 5 minutes.
// You need to implement this functionality.

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Log the execution
$logFile = __DIR__ . '/cron_log.txt';
$timestamp = date('Y-m-d H:i:s');

function logMessage($message)
{
    global $logFile, $timestamp;
    $logEntry = "[{$timestamp}] {$message}\n";
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

try {
    logMessage("CRON job started");

    // Check if registered_emails.txt exists
    $emailFile = __DIR__ . '/registered_emails.txt';

    if (!file_exists($emailFile)) {
        logMessage("registered_emails.txt not found, skipping email send");
        exit(0);
    }

    // Read emails from registered_emails.txt
    $emailContent = file_get_contents($emailFile);

    if (empty(trim($emailContent))) {
        logMessage("No registered emails found in registered_emails.txt, skipping email send");
        exit(0);
    }

    $registeredEmails = array_filter(array_map('trim', explode("\n", trim($emailContent))));

    if (empty($registeredEmails)) {
        logMessage("No valid emails found in registered_emails.txt, skipping email send");
        exit(0);
    }

    logMessage("Found " . count($registeredEmails) . " registered emails in registered_emails.txt");

    // Send GitHub updates to all subscribers
    sendGitHubUpdatesToSubscribers();

    logMessage("GitHub timeline updates sent successfully to " . count($registeredEmails) . " subscribers");
} catch (Exception $e) {
    logMessage("Error occurred: " . $e->getMessage());
    error_log("CRON job error: " . $e->getMessage());
} catch (Throwable $e) {
    logMessage("Fatal error occurred: " . $e->getMessage());
    error_log("CRON job fatal error: " . $e->getMessage());
}

logMessage("CRON job completed");
