[27-06-2025  0:10:48.27] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 2 recipients
[27-06-2025  0:10:48.43] Cron job completed 
[27-06-2025  0:23:47.99] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 2 recipients
[27-06-2025  0:23:48.20] Cron job completed 
[27-06-2025  0:28:25.89] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 2 recipients
[27-06-2025  0:28:26.11] Cron job completed 
[27-06-2025  0:33:25.81] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 2 recipients
[27-06-2025  0:33:25.93] Cron job completed 
